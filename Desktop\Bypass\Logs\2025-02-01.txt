2025-02-01 13:39:13.3  初始化完毕，公网IP：**************


2025-02-01 13:39:13.9  链接12306服务器速度:190毫秒[优]


2025-02-01 13:39:14.2  您的VIP已过期,已恢复为免费用户,续期请重新注册


2025-02-01 13:39:15.6  获取到:498个CDN,开始智能测速中...


2025-02-01 13:39:40.4  [自动处理]默认设置[改签不抢增开列车]，如需更改，请修改增开设置。


2025-02-01 13:39:40.7  查询完毕，查到6个车次，筛选显示6个，用时:285毫秒。


2025-02-01 13:39:46.8  查询完毕，查到6个车次，筛选显示6个，用时:73毫秒。


2025-02-01 13:40:20.7  查询完毕，查到5个车次，筛选显示5个，用时:56毫秒。


2025-02-01 13:40:35.6  [单任务]开始查询2025-02-05车次，间隔：默认


2025-02-01 22:08:25.0  该账号已离线，正在发起重新登录，需要短信验证，已发送提醒！


2025-02-01 22:08:25.9  [微信通知]发送成功！


2025-02-01 22:09:21.8  [检查状态]检测到下线,已成功登录


2025-02-01 22:55:14.1  结束车票查询


2025-02-01 22:56:07.6  添加任务成功，1、[改签]成都东>深圳北_2025-02-05_黎家能[成人];黎于斯[成人];杨舒然[儿童];叶小华[成人]_二等座_G2963


2025-02-01 22:58:12.7  [自动处理]默认设置[改签不抢增开列车]，如需更改，请修改增开设置。


2025-02-01 22:58:12.8  查询完毕，查到20个车次，筛选显示20个，用时:78毫秒。


2025-02-01 22:58:26.1  查询完毕，查到20个车次，筛选显示20个，用时:58毫秒。


2025-02-01 22:59:09.8  查询完毕，查到20个车次，筛选显示20个，用时:55毫秒。


2025-02-01 22:59:19.1  添加任务成功，2、[改签]犀浦>离堆公园_2025-02-03_杨东欣[成人];黎家能[成人];黎于斯[成人];叶小华[成人]_二等座;无座;软座_S5107;S5109;C6347


2025-02-01 23:00:19.8  [自动处理]默认设置[改签不抢增开列车]，如需更改，请修改增开设置。


2025-02-01 23:00:19.9  查询完毕，查到21个车次，筛选显示21个，用时:97毫秒。


2025-02-01 23:01:10.9  查询完毕，查到21个车次，筛选显示21个，用时:55毫秒。


2025-02-01 23:06:41.0  添加任务成功，2、[改签]离堆公园>犀浦_2025-02-03_杨东欣[成人];黎家能[成人];黎于斯[成人];叶小华[成人]_二等座;无座;软座_S5166;S5128;S5126;S5124;S5122


2025-02-01 23:07:12.6  已选坐席包含无座，已关闭[实时余票无座时,不提交]


2025-02-01 23:07:19.2  [多任务]开始查询2025-02-03车次，间隔：默认


2025-02-01 23:34:23.7  [多任务1]犀浦>离堆公园(02-03)的[S5107,软座]...


2025-02-01 23:34:23.7  [预定]线程启动完成,开始解析算法...


2025-02-01 23:34:23.7  [任务1]查询订单并改签中...


2025-02-01 23:34:24.5  [任务1]申请改签成功,申请订单中...


2025-02-01 23:34:24.5  [任务1]算法解析完成,开始申请订单...


2025-02-01 23:34:24.6  [任务2]申请订单成功,开始校验订单...


2025-02-01 23:34:25.0  [任务3]12306无响应,正在尝试


2025-02-01 23:34:25.1  [多任务]开始查询2025-02-03车次，间隔：默认


2025-02-01 23:35:08.8  [多任务1]犀浦>离堆公园(02-03)的[S5107,软座]...


2025-02-01 23:35:08.8  [任务1]查询订单并改签中...


2025-02-01 23:35:09.5  [任务1]申请改签成功,申请订单中...


2025-02-01 23:35:09.6  [任务2]申请订单成功,开始校验订单...


2025-02-01 23:35:09.9  [任务3]12306无响应,正在尝试


2025-02-01 23:35:09.9  [系统忙]等待5秒后，自动重试...


2025-02-01 23:35:14.9  [多任务]开始查询2025-02-03车次，间隔：默认


2025-02-01 23:35:23.5  [多任务1]犀浦>离堆公园(02-03)的[S5107,软座]...


2025-02-01 23:35:23.5  [预定]线程启动完成,开始解析算法...


2025-02-01 23:35:24.2  [任务1]算法解析完成,开始申请订单...


2025-02-01 23:35:24.3  [任务2]申请订单成功,开始校验订单...


2025-02-01 23:35:24.7  [系统忙]等待5秒后，自动重试...


2025-02-01 23:35:29.7  [多任务]开始查询2025-02-03车次，间隔：默认


2025-02-01 23:35:45.5  [多任务1]犀浦>离堆公园(02-03)的[S5107,软座]...


2025-02-01 23:35:45.5  [预定]线程启动完成,开始解析算法...


2025-02-01 23:35:46.2  [任务1]算法解析完成,开始申请订单...


2025-02-01 23:35:46.3  [任务2]申请订单成功,开始校验订单...


2025-02-01 23:35:46.7  [任务3]12306无响应,正在尝试


2025-02-01 23:35:46.7  [系统忙]等待5秒后，自动重试...


2025-02-01 23:35:51.7  [多任务]开始查询2025-02-03车次，间隔：默认


2025-02-01 23:35:51.7  [多任务1]犀浦>离堆公园(02-03)的[S5107,软座]...


2025-02-01 23:35:51.7  [预定]线程启动完成,开始解析算法...


2025-02-01 23:35:52.5  [任务1]申请改签成功,申请订单中...


2025-02-01 23:35:52.6  [任务2]申请订单成功,开始校验订单...


2025-02-01 23:35:53.0  [任务3]12306无响应,正在尝试


2025-02-01 23:35:58.0  [多任务]开始查询2025-02-03车次，间隔：默认


2025-02-01 23:36:02.9  [多任务1]犀浦>离堆公园(02-03)的[S5107,软座]...


2025-02-01 23:36:02.9  [预定]线程启动完成,开始解析算法...


2025-02-01 23:36:03.8  [任务1]算法解析完成,开始申请订单...


2025-02-01 23:36:03.9  [任务2]申请订单成功,开始校验订单...


2025-02-01 23:36:04.3  [任务3]12306无响应,正在尝试


2025-02-01 23:36:04.3  可能是IP被封了，如已重启光猫，请重新开始抢票!


2025-02-01 23:46:04.7  [多任务]开始查询2025-02-03车次，间隔：默认


2025-02-01 23:46:04.8  [多任务1]犀浦>离堆公园(02-03)的[S5107,软座]...


2025-02-01 23:46:04.8  [预定]线程启动完成,开始解析算法...


2025-02-01 23:46:05.6  [任务1]申请改签成功,申请订单中...


2025-02-01 23:46:05.7  [任务2]申请订单成功,开始校验订单...


2025-02-01 23:46:05.9  [任务3]12306无响应,正在尝试


