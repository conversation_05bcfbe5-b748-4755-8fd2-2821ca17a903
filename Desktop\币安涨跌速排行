//@version=6
indicator("币安涨速/跌速排行榜 (v6, 稳定修正版)", overlay=false, max_labels_count=500)

// === 参数 ===
lookbackBars     = input.int(5,  "计算周期（K线根数）", minval=1)
topCount         = input.int(10, "每个榜单显示数量",   minval=1, maxval=50)
volLookbackBars  = input.int(20, "成交额均值的周期（当前周期内）", minval=1)
minNotionalUSDT  = input.float(5e6, "最低平均成交额(USDT, 过滤用)", minval=0.0)

// 精简交易对列表（单行声明）
var string[] symbolList = array.from("BINANCE:BTCUSDT", "BINANCE:ETHUSDT", "BINANCE:BNBUSDT", "BINANCE:SOLUSDT", "BINANCE:XRPUSDT", "BINANCE:DOGEUSDT", "BINANCE:ADAUSDT", "BINANCE:AVAXUSDT", "BINANCE:DOTUSDT", "BINANCE:TRXUSDT", "BINANCE:LINKUSDT", "BINANCE:UNIUSDT", "BINANCE:ATOMUSDT", "BINANCE:NEARUSDT", "BINANCE:OPUSDT", "BINANCE:ARBUSDT", "BINANCE:TONUSDT", "BINANCE:PEPEUSDT", "BINANCE:SUIUSDT", "BINANCE:RNDRUSDT")

// === 优化数据处理 ===
var string[] names  = array.new_string()
var float[]  speeds = array.new_float()

if barstate.islast
    for i = 0 to array.size(symbolList) - 1
        s = array.get(symbolList, i)
        
        // 修正括号闭合问题
        [cNow, cPrev, notionalMA] = request.security(s, timeframe.period,[close, close[lookbackBars], ta.sma(close * volume, volLookbackBars)])
        
        if not na(cPrev) and cPrev != 0 and not na(notionalMA) and notionalMA >= minNotionalUSDT
            spd = (cNow - cPrev) / cPrev * 100.0
            array.push(names, str.replace(s, "BINANCE:", ""))
            array.push(speeds, spd)

// === 冒泡排序实现 ===
// 涨速榜（降序）
namesUp = array.copy(names)
speedsUp = array.copy(speeds)
if array.size(speedsUp) > 0
    for i = 0 to array.size(speedsUp) - 1
        for j = 0 to array.size(speedsUp) - 2
            if array.get(speedsUp, j) < array.get(speedsUp, j + 1)
                // 交换速度
                tempSpeed = array.get(speedsUp, j)
                array.set(speedsUp, j, array.get(speedsUp, j + 1))
                array.set(speedsUp, j + 1, tempSpeed)
                // 交换名称
                tempName = array.get(namesUp, j)
                array.set(namesUp, j, array.get(namesUp, j + 1))
                array.set(namesUp, j + 1, tempName)

// 跌速榜（升序）
namesDown = array.copy(names)
speedsDown = array.copy(speeds)
if array.size(speedsDown) > 0
    for i = 0 to array.size(speedsDown) - 1
        for j = 0 to array.size(speedsDown) - 2
            if array.get(speedsDown, j) > array.get(speedsDown, j + 1)
                // 交换速度
                tempSpeed = array.get(speedsDown, j)
                array.set(speedsDown, j, array.get(speedsDown, j + 1))
                array.set(speedsDown, j + 1, tempSpeed)
                // 交换名称
                tempName = array.get(namesDown, j)
                array.set(namesDown, j, array.get(namesDown, j + 1))
                array.set(namesDown, j + 1, tempName)

// === 表格显示 ===
var tblUp = table.new(position.top_right, 3, topCount + 1, frame_color=color.green)
var tblDn = table.new(position.top_left, 3, topCount + 1, frame_color=color.red)

if barstate.islast
    // 表头
    table.cell(tblUp, 0, 0, "排名", bgcolor=color.gray)
    table.cell(tblUp, 1, 0, "币种", bgcolor=color.gray)
    table.cell(tblUp, 2, 0, "涨速%", bgcolor=color.gray)
    
    table.cell(tblDn, 0, 0, "排名", bgcolor=color.gray)
    table.cell(tblDn, 1, 0, "币种", bgcolor=color.gray)
    table.cell(tblDn, 2, 0, "跌速%", bgcolor=color.gray)
    
    // 填充数据
    topUp = math.min(topCount, array.size(speedsUp))
    for i = 0 to topUp - 1
        table.cell(tblUp, 0, i+1, str.tostring(i+1))
        table.cell(tblUp, 1, i+1, array.get(namesUp, i))
        table.cell(tblUp, 2, i+1, str.tostring(array.get(speedsUp, i), "#.##") + "%")
    
    topDn = math.min(topCount, array.size(speedsDown))
    for i = 0 to topDn - 1
        table.cell(tblDn, 0, i+1, str.tostring(i+1))
        table.cell(tblDn, 1, i+1, array.get(namesDown, i))
        table.cell(tblDn, 2, i+1, str.tostring(array.get(speedsDown, i), "#.##") + "%")