//@version=6
indicator("币安涨跌速排行榜 (v6, 专业优化版)", overlay=false, max_labels_count=500)

// === 参数设置 ===
lookbackBars     = input.int(5,  "计算周期（K线根数）", minval=1, maxval=100)
topCount         = input.int(10, "每个榜单显示数量",   minval=1, maxval=20)
volLookbackBars  = input.int(20, "成交额均值的周期", minval=1, maxval=100)
minNotionalUSDT  = input.float(5e6, "最低平均成交额(USDT)", minval=0.0)
showPrice        = input.bool(true, "显示当前价格")
showVolume       = input.bool(true, "显示24H成交量")
tableSize        = input.string("normal", "表格大小", options=["tiny", "small", "normal", "large"])
refreshRate      = input.int(1, "刷新频率(秒)", minval=1, maxval=60)

// === 交易对列表 ===
var string[] symbolList = array.from(
    "BINANCE:BTCUSDT", "BINANCE:ETHUSDT", "BINANCE:BNBUSDT", "BINANCE:SOLUSDT",
    "BINANCE:XRPUSDT", "BINANCE:DOGEUSDT", "BINANCE:ADAUSDT", "BINANCE:AVAXUSDT",
    "BINANCE:DOTUSDT", "BINANCE:TRXUSDT", "BINANCE:LINKUSDT", "BINANCE:UNIUSDT",
    "BINANCE:ATOMUSDT", "BINANCE:NEARUSDT", "BINANCE:OPUSDT", "BINANCE:ARBUSDT",
    "BINANCE:TONUSDT", "BINANCE:PEPEUSDT", "BINANCE:SUIUSDT", "BINANCE:RNDRUSDT",
    "BINANCE:MATICUSDT", "BINANCE:LTCUSDT", "BINANCE:BCHUSDT", "BINANCE:FILUSDT",
    "BINANCE:APTUSDT", "BINANCE:INJUSDT", "BINANCE:STXUSDT", "BINANCE:TIAUSDT"
)

// === 数据存储结构 ===
type CoinData
    string name
    float speed
    float price
    float volume24h
    color bgColor

var CoinData[] coinDataList = array.new<CoinData>()

// === 颜色函数 ===
getSpeedColor(speed) =>
    if speed >= 10
        color.new(color.lime, 0)
    else if speed >= 5
        color.new(color.green, 20)
    else if speed >= 2
        color.new(color.green, 40)
    else if speed >= 0
        color.new(color.gray, 60)
    else if speed >= -2
        color.new(color.orange, 40)
    else if speed >= -5
        color.new(color.red, 20)
    else
        color.new(color.maroon, 0)

// === 数据获取和处理 ===
if barstate.islast or (time % (refreshRate * 1000) == 0)
    array.clear(coinDataList)

    for i = 0 to array.size(symbolList) - 1
        s = array.get(symbolList, i)

        // 获取价格、成交量数据
        [cNow, cPrev, vol24h, notionalMA] = request.security(s, timeframe.period,
            [close, close[lookbackBars], volume, ta.sma(close * volume, volLookbackBars)])

        if not na(cPrev) and cPrev != 0 and not na(notionalMA) and notionalMA >= minNotionalUSDT
            spd = (cNow - cPrev) / cPrev * 100.0
            coinName = str.replace(s, "BINANCE:", "")

            // 创建币种数据
            coinData = CoinData.new(
                name = coinName,
                speed = spd,
                price = cNow,
                volume24h = vol24h * cNow,
                bgColor = getSpeedColor(spd)
            )

            array.push(coinDataList, coinData)

// === 冒泡排序实现 ===
// 涨速榜（降序）
namesUp = array.copy(names)
speedsUp = array.copy(speeds)
if array.size(speedsUp) > 0
    for i = 0 to array.size(speedsUp) - 1
        for j = 0 to array.size(speedsUp) - 2
            if array.get(speedsUp, j) < array.get(speedsUp, j + 1)
                // 交换速度
                tempSpeed = array.get(speedsUp, j)
                array.set(speedsUp, j, array.get(speedsUp, j + 1))
                array.set(speedsUp, j + 1, tempSpeed)
                // 交换名称
                tempName = array.get(namesUp, j)
                array.set(namesUp, j, array.get(namesUp, j + 1))
                array.set(namesUp, j + 1, tempName)

// 跌速榜（升序）
namesDown = array.copy(names)
speedsDown = array.copy(speeds)
if array.size(speedsDown) > 0
    for i = 0 to array.size(speedsDown) - 1
        for j = 0 to array.size(speedsDown) - 2
            if array.get(speedsDown, j) > array.get(speedsDown, j + 1)
                // 交换速度
                tempSpeed = array.get(speedsDown, j)
                array.set(speedsDown, j, array.get(speedsDown, j + 1))
                array.set(speedsDown, j + 1, tempSpeed)
                // 交换名称
                tempName = array.get(namesDown, j)
                array.set(namesDown, j, array.get(namesDown, j + 1))
                array.set(namesDown, j + 1, tempName)

// === 表格显示 ===
var tblUp = table.new(position.top_right, 3, topCount + 1, frame_color=color.green)
var tblDn = table.new(position.top_left, 3, topCount + 1, frame_color=color.red)

if barstate.islast
    // 表头
    table.cell(tblUp, 0, 0, "排名", bgcolor=color.gray)
    table.cell(tblUp, 1, 0, "币种", bgcolor=color.gray)
    table.cell(tblUp, 2, 0, "涨速%", bgcolor=color.gray)
    
    table.cell(tblDn, 0, 0, "排名", bgcolor=color.gray)
    table.cell(tblDn, 1, 0, "币种", bgcolor=color.gray)
    table.cell(tblDn, 2, 0, "跌速%", bgcolor=color.gray)
    
    // 填充数据
    topUp = math.min(topCount, array.size(speedsUp))
    for i = 0 to topUp - 1
        table.cell(tblUp, 0, i+1, str.tostring(i+1))
        table.cell(tblUp, 1, i+1, array.get(namesUp, i))
        table.cell(tblUp, 2, i+1, str.tostring(array.get(speedsUp, i), "#.##") + "%")
    
    topDn = math.min(topCount, array.size(speedsDown))
    for i = 0 to topDn - 1
        table.cell(tblDn, 0, i+1, str.tostring(i+1))
        table.cell(tblDn, 1, i+1, array.get(namesDown, i))
        table.cell(tblDn, 2, i+1, str.tostring(array.get(speedsDown, i), "#.##") + "%")