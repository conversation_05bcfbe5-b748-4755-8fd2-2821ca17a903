//@version=6
indicator("币安涨跌速排行榜 (v6, 专业优化版)", overlay=false, max_labels_count=500)

// === 参数设置 ===
lookbackBars     = input.int(5,  "计算周期（K线根数）", minval=1, maxval=100)
topCount         = input.int(10, "每个榜单显示数量",   minval=1, maxval=20)
volLookbackBars  = input.int(20, "成交额均值的周期", minval=1, maxval=100)
minNotionalUSDT  = input.float(5e6, "最低平均成交额(USDT)", minval=0.0)
showPrice        = input.bool(true, "显示当前价格")
showVolume       = input.bool(true, "显示24H成交量")
tableSize        = input.string("normal", "表格大小", options=["tiny", "small", "normal", "large"])
refreshRate      = input.int(1, "刷新频率(秒)", minval=1, maxval=60)

// === 交易对列表 ===
var string[] symbolList = array.from(
    "BINANCE:BTCUSDT", "BINANCE:ETHUSDT", "BINANCE:BNBUSDT", "BINANCE:SOLUSDT",
    "BINANCE:XRPUSDT", "BINANCE:DOGEUSDT", "BINANCE:ADAUSDT", "BINANCE:AVAXUSDT",
    "BINANCE:DOTUSDT", "BINANCE:TRXUSDT", "BINANCE:LINKUSDT", "BINANCE:UNIUSDT",
    "BINANCE:ATOMUSDT", "BINANCE:NEARUSDT", "BINANCE:OPUSDT", "BINANCE:ARBUSDT",
    "BINANCE:TONUSDT", "BINANCE:PEPEUSDT", "BINANCE:SUIUSDT", "BINANCE:RNDRUSDT",
    "BINANCE:MATICUSDT", "BINANCE:LTCUSDT", "BINANCE:BCHUSDT", "BINANCE:FILUSDT",
    "BINANCE:APTUSDT", "BINANCE:INJUSDT", "BINANCE:STXUSDT", "BINANCE:TIAUSDT"
)

// === 数据存储结构 ===
type CoinData
    string name
    float speed
    float price
    float volume24h
    color bgColor

var coinDataList = array.new<CoinData>()

// === 颜色函数 ===
getSpeedColor(speed) =>
    if speed >= 10
        color.new(color.lime, 0)
    else if speed >= 5
        color.new(color.green, 20)
    else if speed >= 2
        color.new(color.green, 40)
    else if speed >= 0
        color.new(color.gray, 60)
    else if speed >= -2
        color.new(color.orange, 40)
    else if speed >= -5
        color.new(color.red, 20)
    else
        color.new(color.maroon, 0)

// === 数据获取和处理 ===
if barstate.islast
    array.clear(coinDataList)

    for i = 0 to array.size(symbolList) - 1
        s = array.get(symbolList, i)

        // 获取价格、成交量数据
        [cNow, cPrev, vol24h, notionalMA] = request.security(s, timeframe.period, [close, close[lookbackBars], volume, ta.sma(close * volume, volLookbackBars)])

        if not na(cPrev) and cPrev != 0 and not na(notionalMA) and notionalMA >= minNotionalUSDT
            spd = (cNow - cPrev) / cPrev * 100.0
            coinName = str.replace(s, "BINANCE:", "")

            // 创建币种数据
            coinData = CoinData.new(coinName, spd, cNow, vol24h * cNow, getSpeedColor(spd))

            array.push(coinDataList, coinData)

// === 排序实现 ===
var coinsUp = array.new<CoinData>()
var coinsDown = array.new<CoinData>()

if barstate.islast
    // 清空之前的排序结果
    array.clear(coinsUp)
    array.clear(coinsDown)

    // 复制数据到排序数组
    for i = 0 to array.size(coinDataList) - 1
        coinData = array.get(coinDataList, i)
        array.push(coinsUp, coinData)
        array.push(coinsDown, coinData)

    // 涨速榜排序（降序）- 选择排序算法
    if array.size(coinsUp) > 1
        for i = 0 to array.size(coinsUp) - 2
            maxIdx = i
            for j = i + 1 to array.size(coinsUp) - 1
                if array.get(coinsUp, j).speed > array.get(coinsUp, maxIdx).speed
                    maxIdx := j
            if maxIdx != i
                temp = array.get(coinsUp, i)
                array.set(coinsUp, i, array.get(coinsUp, maxIdx))
                array.set(coinsUp, maxIdx, temp)

    // 跌速榜排序（升序）- 选择排序算法
    if array.size(coinsDown) > 1
        for i = 0 to array.size(coinsDown) - 2
            minIdx = i
            for j = i + 1 to array.size(coinsDown) - 1
                if array.get(coinsDown, j).speed < array.get(coinsDown, minIdx).speed
                    minIdx := j
            if minIdx != i
                temp = array.get(coinsDown, i)
                array.set(coinsDown, i, array.get(coinsDown, minIdx))
                array.set(coinsDown, minIdx, temp)

// === 格式化函数 ===
formatPrice(price) =>
    if price >= 1000
        str.tostring(price, "#,###.##")
    else if price >= 1
        str.tostring(price, "#.####")
    else
        str.tostring(price, "#.######")

formatVolume(vol) =>
    if vol >= 1e9
        str.tostring(vol / 1e9, "#.##") + "B"
    else if vol >= 1e6
        str.tostring(vol / 1e6, "#.##") + "M"
    else if vol >= 1e3
        str.tostring(vol / 1e3, "#.##") + "K"
    else
        str.tostring(vol, "#.##")

// === 表格显示 ===
// 动态计算表格列数
tableColumns = showPrice and showVolume ? 5 : (showPrice or showVolume ? 4 : 3)

var tblUp = table.new(position.top_right, tableColumns, topCount + 2, frame_color=color.new(color.green, 30), frame_width=2)
var tblDn = table.new(position.top_left, tableColumns, topCount + 2, frame_color=color.new(color.red, 30), frame_width=2)

// 获取表格大小设置
getTableSize() =>
    switch tableSize
        "tiny" => size.tiny
        "small" => size.small
        "normal" => size.normal
        "large" => size.large
        => size.normal

if barstate.islast
    // 清空表格
    table.clear(tblUp)
    table.clear(tblDn)

    // 涨速榜标题
    table.cell(tblUp, 0, 0, "🚀 涨速排行榜", bgcolor=color.new(color.green, 20), text_color=color.white, text_size=getTableSize())
    if tableColumns > 1
        table.merge_cells(tblUp, 0, 0, tableColumns - 1, 0)

    // 跌速榜标题
    table.cell(tblDn, 0, 0, "📉 跌速排行榜", bgcolor=color.new(color.red, 20), text_color=color.white, text_size=getTableSize())
    if tableColumns > 1
        table.merge_cells(tblDn, 0, 0, tableColumns - 1, 0)

    // 表头
    col = 0
    table.cell(tblUp, col, 1, "排名", bgcolor=color.new(color.gray, 40), text_color=color.white, text_size=getTableSize())
    table.cell(tblDn, col, 1, "排名", bgcolor=color.new(color.gray, 40), text_color=color.white, text_size=getTableSize())
    col += 1

    table.cell(tblUp, col, 1, "币种", bgcolor=color.new(color.gray, 40), text_color=color.white, text_size=getTableSize())
    table.cell(tblDn, col, 1, "币种", bgcolor=color.new(color.gray, 40), text_color=color.white, text_size=getTableSize())
    col += 1

    table.cell(tblUp, col, 1, "涨速%", bgcolor=color.new(color.gray, 40), text_color=color.white, text_size=getTableSize())
    table.cell(tblDn, col, 1, "跌速%", bgcolor=color.new(color.gray, 40), text_color=color.white, text_size=getTableSize())
    col += 1

    if showPrice
        table.cell(tblUp, col, 1, "价格", bgcolor=color.new(color.gray, 40), text_color=color.white, text_size=getTableSize())
        table.cell(tblDn, col, 1, "价格", bgcolor=color.new(color.gray, 40), text_color=color.white, text_size=getTableSize())
        col += 1

    if showVolume
        table.cell(tblUp, col, 1, "24H量", bgcolor=color.new(color.gray, 40), text_color=color.white, text_size=getTableSize())
        table.cell(tblDn, col, 1, "24H量", bgcolor=color.new(color.gray, 40), text_color=color.white, text_size=getTableSize())

    // 填充涨速榜数据
    topUp = math.min(topCount, array.size(coinsUp))
    for i = 0 to topUp - 1
        coinData = array.get(coinsUp, i)
        row = i + 2
        col := 0

        // 排名
        table.cell(tblUp, col, row, str.tostring(i + 1),
            bgcolor=coinData.bgColor, text_color=color.white, text_size=getTableSize())
        col += 1

        // 币种
        table.cell(tblUp, col, row, coinData.name,
            bgcolor=coinData.bgColor, text_color=color.white, text_size=getTableSize())
        col += 1

        // 涨速
        table.cell(tblUp, col, row, str.tostring(coinData.speed, "#.##") + "%",
            bgcolor=coinData.bgColor, text_color=color.white, text_size=getTableSize())
        col += 1

        // 价格
        if showPrice
            table.cell(tblUp, col, row, "$" + formatPrice(coinData.price),
                bgcolor=coinData.bgColor, text_color=color.white, text_size=getTableSize())
            col += 1

        // 成交量
        if showVolume
            table.cell(tblUp, col, row, "$" + formatVolume(coinData.volume24h),
                bgcolor=coinData.bgColor, text_color=color.white, text_size=getTableSize())

    // 填充跌速榜数据
    topDn = math.min(topCount, array.size(coinsDown))
    for i = 0 to topDn - 1
        coinData = array.get(coinsDown, i)
        row = i + 2
        col := 0

        // 排名
        table.cell(tblDn, col, row, str.tostring(i + 1),
            bgcolor=coinData.bgColor, text_color=color.white, text_size=getTableSize())
        col += 1

        // 币种
        table.cell(tblDn, col, row, coinData.name,
            bgcolor=coinData.bgColor, text_color=color.white, text_size=getTableSize())
        col += 1

        // 跌速
        table.cell(tblDn, col, row, str.tostring(coinData.speed, "#.##") + "%",
            bgcolor=coinData.bgColor, text_color=color.white, text_size=getTableSize())
        col += 1

        // 价格
        if showPrice
            table.cell(tblDn, col, row, "$" + formatPrice(coinData.price),
                bgcolor=coinData.bgColor, text_color=color.white, text_size=getTableSize())
            col += 1

        // 成交量
        if showVolume
            table.cell(tblDn, col, row, "$" + formatVolume(coinData.volume24h),
                bgcolor=coinData.bgColor, text_color=color.white, text_size=getTableSize())