2025-01-26 13:50:01.2  初始化完毕，公网IP：*************


2025-01-26 13:50:02.3  链接12306服务器速度:210毫秒[优]


2025-01-26 13:50:02.5  您的VIP已过期,已恢复为免费用户,续期请重新注册


2025-01-26 13:50:03.4  获取到:498个CDN,开始智能测速中...


2025-01-26 13:51:03.2  [自动处理]默认设置[改签不抢增开列车]，如需更改，请修改增开设置。


2025-01-26 13:51:03.5  查询完毕，查到6个车次，筛选显示6个，用时:293毫秒。


2025-01-26 13:51:03.6  链接12306服务器速度:91毫秒[优]


2025-01-26 13:51:07.7  查询完毕，查到5个车次，筛选显示5个，用时:90毫秒。


2025-01-26 13:52:02.1  查询完毕，查到5个车次，筛选显示5个，用时:109毫秒。


2025-01-26 13:52:07.1  [单任务]开始查询2025-02-05车次，间隔：默认


2025-01-26 14:47:36.3  检测到网络不稳定，请检查网络！


2025-01-26 14:48:12.5  检测到网络不稳定，请检查网络！


2025-01-26 16:38:15.6  WebView2意外结束，0，正在重新启动...


2025-01-26 16:39:00.4  该账号已离线，正在发起重新登录，需要短信验证，已发送提醒！


2025-01-26 16:39:02.7  [微信通知]发送失败，服务器无响应，自动重试。


2025-01-26 16:39:08.6  多次查询数据无效，可能公网IP被封！


2025-01-26 16:39:16.7  [微信通知]发送失败，服务器无响应，自动重试。


2025-01-26 16:39:46.7  [微信通知]发送失败，服务器无响应，自动重试。


2025-01-26 16:40:09.8  多次查询数据无效，可能公网IP被封！


2025-01-26 16:40:36.9  [微信通知]发送失败，服务器无响应，自动重试。


2025-01-26 16:41:15.0  [微信通知]发送失败，服务器无响应，自动重试。


2025-01-26 16:42:02.9  多次查询数据无效，可能公网IP被封！


2025-01-26 16:42:29.0  [微信通知]发送失败，服务器无响应，自动重试。


2025-01-26 16:43:55.7  多次查询数据无效，可能公网IP被封！


2025-01-26 16:44:19.1  [微信通知]发送失败，服务器无响应，自动重试。


2025-01-26 16:44:57.5  多次查询数据无效，可能公网IP被封！


2025-01-26 16:45:33.1  [微信通知]发送失败，服务器无响应，自动重试。


2025-01-26 16:46:04.2  多次查询数据无效，可能公网IP被封！


2025-01-26 16:46:44.7  [检查状态]该账号离线，需要短信登录，用户取消了操作！[-1]


2025-01-26 16:46:44.7  结束车票查询


2025-01-26 16:46:48.8  [微信通知]发送失败，服务器无响应，自动重试。


2025-01-26 16:47:00.9  [微信通知]发送失败，服务器无响应，自动重试。


2025-01-26 16:47:08.6  [单任务]开始查询2025-02-05车次，间隔：默认


2025-01-26 16:47:12.9  [微信通知]发送失败，服务器无响应，自动重试。


2025-01-26 16:47:19.2  [微信通知]发送失败，服务器无响应，自动重试。


2025-01-26 16:47:45.0  [微信通知]发送失败，服务器无响应，自动重试。


2025-01-26 16:47:58.6  多次查询数据无效，可能公网IP被封！


2025-01-26 16:48:33.7  [微信通知]发送成功！


2025-01-26 16:48:36.7  [检查状态]检测到下线,已成功登录


2025-01-26 16:50:02.2  [微信通知]发送成功！


2025-01-26 16:53:44.9  多次查询数据无效，可能公网IP被封！


2025-01-26 16:54:45.7  多次查询数据无效，可能公网IP被封！


2025-01-26 16:55:03.3  结束车票查询


2025-01-26 16:55:05.9  [单任务]开始查询2025-02-05车次，间隔：默认


