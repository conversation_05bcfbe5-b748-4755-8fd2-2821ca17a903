// 此源代码受 Mozilla Public License 2.0 条款约束，详见 https://mozilla.org/MPL/2.0/
// © DX

//@version=6
// 唐安奇趋势突破策略
// Donchian Channel Breakout Strategy
strategy(title="唐安奇趋势突破策略 (Donchian Breakout Strategy)", shorttitle="DCS", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=25)

// ==================== 用户输入参数设置 ====================

// 唐安奇通道周期设置
upper_length = input.int(title="上趋势线周期 (Upper Channel Period)", defval=20, minval=1, maxval=500, group="通道参数")
lower_length = input.int(title="下趋势线周期 (Lower Channel Period)", defval=10, minval=1, maxval=500, group="通道参数")

// 颜色配置
upper_color = input.color(title="上趋势线颜色 (Upper Line Color)", defval=color.red, group="颜色设置")
middle_color = input.color(title="中线颜色 (Middle Line Color)", defval=color.blue, group="颜色设置")
lower_color = input.color(title="下趋势线颜色 (Lower Line Color)", defval=color.green, group="颜色设置")

// 背景填充设置
fill_upper = input.bool(title="填充上通道", defval=true, group="填充设置")
fill_lower = input.bool(title="填充下通道", defval=true, group="填充设置")
fill_transparency = input.int(title="填充透明度", defval=90, minval=0, maxval=100, group="填充设置")

// 策略设置
enable_long = input.bool(title="启用做多信号", defval=true, group="策略设置")
enable_short = input.bool(title="启用做空信号", defval=true, group="策略设置")
use_stop_loss = input.bool(title="使用ATR止损", defval=true, group="策略设置")
atr_length = input.int(title="ATR周期", defval=14, minval=1, maxval=100, group="策略设置")
atr_multiplier = input.float(title="ATR倍数", defval=2.0, minval=0.5, maxval=10.0, step=0.1, group="策略设置")

// 信号显示设置
show_signals = input.bool(title="显示买卖信号", defval=true, group="显示设置")
show_labels = input.bool(title="显示标签", defval=true, group="显示设置")

// ==================== 唐安奇通道计算 ====================

// 计算唐安奇通道
// 上趋势线：指定周期内的最高价
upper_channel = ta.highest(high, upper_length)

// 下趋势线：指定周期内的最低价
lower_channel = ta.lowest(low, lower_length)

// 中线：上下趋势线的均值
middle_channel = (upper_channel + lower_channel) / 2

// 计算ATR用于止损
atr_value = ta.atr(atr_length)

// 声明全局变量用于存储止损价格
var float long_entry_price = na
var float short_entry_price = na
var float long_stop_price = na
var float short_stop_price = na

// ==================== 突破信号检测 ====================

// 向上突破信号：当前收盘价突破前一根K线的上趋势线
// 使用前一根K线的通道值，避免未来数据泄露
long_signal = ta.crossover(close, upper_channel[1])

// 向下突破信号：当前收盘价跌破前一根K线的下趋势线
short_signal = ta.crossunder(close, lower_channel[1])

// 额外条件：确保突破是有效的（避免在数据不足时触发假信号）
valid_data = bar_index >= math.max(upper_length, lower_length)
long_signal := long_signal and valid_data
short_signal := short_signal and valid_data

// ==================== 绘制唐安奇通道 ====================

// 绘制上趋势线
plot_upper = plot(upper_channel, title="上趋势线", color=upper_color, linewidth=2)

// 绘制中线
plot_middle = plot(middle_channel, title="中线", color=middle_color, linewidth=1, style=plot.style_line)

// 绘制下趋势线
plot_lower = plot(lower_channel, title="下趋势线", color=lower_color, linewidth=2)

// 背景填充
fill(plot_upper, plot_middle, color=fill_upper ? color.new(upper_color, fill_transparency) : na, title="上通道填充")
fill(plot_middle, plot_lower, color=fill_lower ? color.new(lower_color, fill_transparency) : na, title="下通道填充")

// 绘制ATR止损线
plot(strategy.position_size > 0 ? long_stop_price : na, title="做多止损线", color=color.red, linewidth=1, style=plot.style_stepline)
plot(strategy.position_size < 0 ? short_stop_price : na, title="做空止损线", color=color.red, linewidth=1, style=plot.style_stepline)

// ==================== 策略执行 ====================

// 执行做多策略（只在没有仓位时开仓，有空头仓位时通过反向平仓处理）
if enable_long and long_signal and strategy.position_size == 0
    long_entry_price := close
    strategy.entry("做多", strategy.long, comment="突破买入")

    // 计算ATR止损价格
    if use_stop_loss
        long_stop_price := long_entry_price - (atr_value * atr_multiplier)

    // 发送买入通知
    alert("🚀 突破买入信号！\n价格: " + str.tostring(close, "#.####") +
          "\n止损: " + str.tostring(long_stop_price, "#.####"), alert.freq_once_per_bar)

// 调试信息：在突破但未开仓时显示原因
if long_signal and not (enable_long and long_signal and strategy.position_size == 0)
    debug_reason = ""
    if not enable_long
        debug_reason := "做多被禁用"
    else if strategy.position_size > 0
        debug_reason := "已有多头仓位"
    else if strategy.position_size < 0
        debug_reason := "有空头仓位\n等待反向平仓"
    else if not valid_data
        debug_reason := "数据不足"

    // 显示调试标签
    if debug_reason != ""
        label.new(bar_index, high + (high - low) * 0.15, text=debug_reason,
                 style=label.style_label_down, color=color.orange, textcolor=color.white, size=size.small)

// 精简的调试信息：显示关键状态
if long_signal and barstate.isconfirmed
    position_status = strategy.position_size > 0 ? "多头" : strategy.position_size < 0 ? "空头" : "无仓位"

    // 检查开仓条件
    can_open_long = enable_long and long_signal and strategy.position_size == 0

    status_text = "仓位: " + position_status + "\n"
    status_text += "可开多: " + str.tostring(can_open_long)

    label.new(bar_index, low - (high - low) * 0.1, text=status_text,
             style=label.style_label_up, color=color.blue, textcolor=color.white, size=size.small)

// 执行做空策略（只在没有仓位时开仓，有多头仓位时通过反向平仓处理）
if enable_short and short_signal and strategy.position_size == 0
    short_entry_price := close
    strategy.entry("做空", strategy.short, comment="跌破卖出")

    // 计算ATR止损价格
    if use_stop_loss
        short_stop_price := short_entry_price + (atr_value * atr_multiplier)

    // 发送卖出通知
    alert("📉 跌破卖出信号！\n价格: " + str.tostring(close, "#.####") +
          "\n止损: " + str.tostring(short_stop_price, "#.####"), alert.freq_once_per_bar)

// 基于收盘价的ATR止损
if use_stop_loss and strategy.position_size > 0 and not na(long_stop_price)
    // 做多止损：收盘价跌破止损位
    if close <= long_stop_price
        strategy.close("做多", comment="收盘价ATR止损")

        // 发送止损通知
        alert("🛑 多头止损！\n价格: " + str.tostring(close, "#.####") +
              "\n止损价: " + str.tostring(long_stop_price, "#.####") +
              "\n原因: ATR止损", alert.freq_once_per_bar)

        long_stop_price := na

if use_stop_loss and strategy.position_size < 0 and not na(short_stop_price)
    // 做空止损：收盘价突破止损位
    if close >= short_stop_price
        strategy.close("做空", comment="收盘价ATR止损")

        // 发送止损通知
        alert("🛑 空头止损！\n价格: " + str.tostring(close, "#.####") +
              "\n止损价: " + str.tostring(short_stop_price, "#.####") +
              "\n原因: ATR止损", alert.freq_once_per_bar)

        short_stop_price := na

// 平仓条件：反向信号
if strategy.position_size > 0 and short_signal
    strategy.close("做多", comment="反向平仓")

    // 发送反向平仓通知
    alert("🔄 多头反向平仓！\n价格: " + str.tostring(close, "#.####") +
          "\n原因: 跌破下趋势线", alert.freq_once_per_bar)

    long_stop_price := na  // 清除止损价格

if strategy.position_size < 0 and long_signal
    strategy.close("做空", comment="反向平仓")

    // 发送反向平仓通知
    alert("🔄 空头反向平仓！\n价格: " + str.tostring(close, "#.####") +
          "\n原因: 突破上趋势线", alert.freq_once_per_bar)

    short_stop_price := na  // 清除止损价格

// ==================== 信号显示 ====================

// 可选：显示突破信号形状标记（与策略信号不重复）
// 向上突破标记
plotshape(series=show_signals and long_signal, title="向上突破", location=location.belowbar, color=color.green, style=shape.triangleup, size=size.small)

// 向下突破标记
plotshape(series=show_signals and short_signal, title="向下突破", location=location.abovebar, color=color.red, style=shape.triangledown, size=size.small)

// 注释：移除了信息表格显示功能，避免图表上出现白色参数框
// 如需查看参数信息，可以在策略设置中查看或通过指标数值显示

// ==================== 警报设置 ====================

// 设置警报条件
alertcondition(long_signal, title="买入信号", message="价格突破上趋势线，建议买入")
alertcondition(short_signal, title="卖出信号", message="价格跌破下趋势线，建议卖出")

// ==================== 策略说明 ====================
// 唐安奇通道突破策略说明：
// 1. 上趋势线：指定周期内的最高价，作为阻力位
// 2. 下趋势线：指定周期内的最低价，作为支撑位
// 3. 中线：上下趋势线的均值，作为趋势参考
// 4. 买入信号：价格向上突破上趋势线时
// 5. 卖出信号：价格向下跌破下趋势线时
// 6. 可配置不同的上下通道周期，适应不同的市场环境
// 7. ATR动态止损：开仓后价格向亏损方向波动2倍ATR时止损
