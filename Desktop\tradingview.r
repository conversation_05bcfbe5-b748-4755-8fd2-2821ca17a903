// 此源代码受 Mozilla Public License 2.0 条款约束，详见 https://mozilla.org/MPL/2.0/
// © Pulu_

//@version=5
// Pulu 的移动平均线指标
// 发布版本 1.68，日期 2021-12-05
indicator(title='Pulu\'s Moving Averages', shorttitle='PMA', overlay=true)

// 数值格式化函数：根据数值大小选择合适的显示格式
// 参数：num - 需要格式化的数值
// 返回：格式化后的字符串
strRoundValue(num) =>
    strv = ''
    if num >= 100000                                    // 大于等于10万：显示为K格式（如：150K）
        strv := str.tostring(num/1000, '#K')
    else if (num < 100000) and (num >= 100)            // 100到10万之间：显示整数
        strv := str.tostring(num, '#')
    else if (num < 100) and (num >= 1)                 // 1到100之间：显示2位小数
        strv := str.tostring(num, '#.##')
    else if (num < 1) and (num >= 0.01)                // 0.01到1之间：显示4位小数
        strv := str.tostring(num, '#.####')
    else if (num < 0.01) and (num >= 0.0001)           // 0.0001到0.01之间：显示6位小数
        strv := str.tostring(num, '#.######')
    else if (num < 0.0001) and (num >= 0.000001)       // 0.000001到0.0001之间：显示8位小数
        strv := str.tostring(num, '#.########')
    (strv)

// 默认移动平均线计算函数：根据指定类型计算移动平均线
// 参数：
//   func - 移动平均线类型（字符串）
//   src - 数据源（通常是价格数据）
//   len - 周期长度
//   alma_offst - ALMA偏移参数
//   alma_sigma - ALMA西格玛参数
// 返回：[移动平均线值, 是否需要周期长度参数]
defaultFunction(func, src, len, alma_offst, alma_sigma) =>
    has_len = false                                     // 标记是否需要周期长度参数
    ma = ta.swma(close)                                 // 默认使用对称加权移动平均线
    if func == 'ALMA'                                   // Arnaud Legoux移动平均线
        ma := ta.alma(src, len, alma_offst, alma_sigma)
        has_len := true
        has_len
    else if func == 'EMA'                               // 指数移动平均线
        ma := ta.ema(src, len)
        has_len := true
        has_len
    else if func == 'RMA'                               // 相对强度移动平均线（Wilder平滑）
        ma := ta.rma(src, len)
        has_len := true
        has_len
    else if func == 'SMA'                               // 简单移动平均线
        ma := ta.sma(src, len)
        has_len := true
        has_len
    else if func == 'SWMA'                              // 对称加权移动平均线（不需要周期参数）
        ma := ta.swma(src)
        has_len := false
        has_len
    else if func == 'VWAP'                              // 成交量加权平均价格（不需要周期参数）
        ma := ta.vwap(src)
        has_len := false
        has_len
    else if func == 'VWMA'                              // 成交量加权移动平均线
        ma := ta.vwma(src, len)
        has_len := true
        has_len
    else if func == 'WMA'                               // 线性加权移动平均线
        ma := ta.wma(src, len)
        has_len := true
        has_len
    [ma, has_len]

// ==================== 用户输入参数设置 ====================

// 默认移动平均线类型选择
def_fn = input.string(title='Default moving average', defval='EMA', options=['ALMA', 'EMA', 'RMA', 'SMA', 'SWMA', 'VWAP', 'VWMA', 'WMA'])

// 移动平均线开关设置：控制每条移动平均线是否显示
ma1_on = input.bool(inline='MA1', title='Enable moving average 1', defval=true)
ma2_on = input.bool(inline='MA2', title='Enable moving average 2', defval=true)
ma3_on = input.bool(inline='MA3', title='Enable moving average 3', defval=true)
ma4_on = input.bool(inline='MA4', title='Enable moving average 4', defval=true)
ma5_on = input.bool(inline='MA5', title='Enable moving average 5', defval=true)
ma6_on = input.bool(inline='MA6', title='Enable moving average 6', defval=true)
ma7_on = input.bool(inline='MA7', title='Enable moving average 7', defval=true)

// 移动平均线类型设置：每条线可以选择不同的计算方法
ma1_fn = input.string(inline='MA1', title='', defval='default', options=['default', 'ALMA', 'EMA', 'RMA', 'SMA', 'SWMA', 'VWAP', 'VWMA', 'WMA'])
ma2_fn = input.string(inline='MA2', title='', defval='default', options=['default', 'ALMA', 'EMA', 'RMA', 'SMA', 'SWMA', 'VWAP', 'VWMA', 'WMA'])
ma3_fn = input.string(inline='MA3', title='', defval='default', options=['default', 'ALMA', 'EMA', 'RMA', 'SMA', 'SWMA', 'VWAP', 'VWMA', 'WMA'])
ma4_fn = input.string(inline='MA4', title='', defval='default', options=['default', 'ALMA', 'EMA', 'RMA', 'SMA', 'SWMA', 'VWAP', 'VWMA', 'WMA'])
ma5_fn = input.string(inline='MA5', title='', defval='default', options=['default', 'ALMA', 'EMA', 'RMA', 'SMA', 'SWMA', 'VWAP', 'VWMA', 'WMA'])
ma6_fn = input.string(inline='MA6', title='', defval='default', options=['default', 'ALMA', 'EMA', 'RMA', 'SMA', 'SWMA', 'VWAP', 'VWMA', 'WMA'])
ma7_fn = input.string(inline='MA7', title='', defval='default', options=['default', 'ALMA', 'EMA', 'RMA', 'SMA', 'SWMA', 'VWAP', 'VWMA', 'WMA'])

// 移动平均线周期设置：定义每条线的计算周期（K线数量）
ma1_len = input.int(inline='MA1', title='', defval=12, minval=1)    // 短期：12周期
ma2_len = input.int(inline='MA2', title='', defval=144, minval=1)   // 中期：144周期
ma3_len = input.int(inline='MA3', title='', defval=169, minval=1)   // 中期：169周期
ma4_len = input.int(inline='MA4', title='', defval=288, minval=1)   // 长期：288周期
ma5_len = input.int(inline='MA5', title='', defval=338, minval=1)   // 长期：338周期
ma6_len = input.int(inline='MA6', title='', defval=576, minval=1)   // 超长期：576周期
ma7_len = input.int(inline='MA7', title='', defval=676, minval=1)   // 超长期：676周期

// 移动平均线颜色设置：为每条线设置不同的显示颜色
ma1_clr = input.color(inline='MA1', title='', defval=color.fuchsia)  // 紫红色
ma2_clr = input.color(inline='MA2', title='', defval=color.aqua)     // 青色
ma3_clr = input.color(inline='MA3', title='', defval=color.yellow)   // 黄色
ma4_clr = input.color(inline='MA4', title='', defval=color.blue)     // 蓝色
ma5_clr = input.color(inline='MA5', title='', defval=color.orange)   // 橙色
ma6_clr = input.color(inline='MA6', title='', defval=color.green)    // 绿色
ma7_clr = input.color(inline='MA7', title='', defval=color.red)      // 红色

// 计算数组索引：将周期长度转换为数组索引（索引从0开始，所以减1）
ma1_len_indx = ma1_len - 1
ma2_len_indx = ma2_len - 1
ma3_len_indx = ma3_len - 1
ma4_len_indx = ma4_len - 1
ma5_len_indx = ma5_len - 1
ma6_len_indx = ma6_len - 1
ma7_len_indx = ma7_len - 1

// ==================== 移动平均线详细参数设置 ====================

// 移动平均线1的其他参数
alma1_offst = input.float(group='MA1 other settings', inline='MA11', title='ALMA offset', defval=0.85, minval=-1, maxval=1, step=0.01)    // ALMA偏移量：控制平均线的响应性
alma1_sigma = input.float(group='MA1 other settings', inline='MA11', title=', sigma', defval=6, minval=0, maxval=100, step=0.01)          // ALMA西格玛：控制平滑度
ma1_src = input.source(group='MA1 other settings', inline='MA12', title='Source', defval=close)                                           // 数据源：默认使用收盘价
ma1_plt_offst = input.int(group='MA1 other settings', inline='MA12', title=', plot offset', defval=0, minval=-500, maxval=500)           // 绘图偏移：向前或向后偏移显示

// 移动平均线2的其他参数
alma2_offst = input.float(group='MA2 other settings', inline='MA21', title='ALMA Offset', defval=0.85, minval=-1, maxval=1, step=0.01)
alma2_sigma = input.float(group='MA2 other settings', inline='MA21', title='Sigma', defval=6, minval=0, maxval=100, step=0.01)
ma2_src = input.source(group='MA2 other settings', inline='MA22', title='Source', defval=close)
ma2_plt_offst = input.int(group='MA2 other settings', inline='MA22', title='Polt offset', defval=0, minval=-500, maxval=500)

// 移动平均线3的其他参数
alma3_offst = input.float(group='MA3 other settings', inline='MA31', title='ALMA Offset', defval=0.85, minval=-1, maxval=1, step=0.01)
alma3_sigma = input.float(group='MA3 other settings', inline='MA31', title='Sigma', defval=6, minval=0, maxval=100, step=0.01)
ma3_src = input.source(group='MA3 other settings', inline='MA32', title='Source', defval=close)
ma3_plt_offst = input.int(group='MA3 other settings', inline='MA32', title='Plot offset', defval=0, minval=-500, maxval=500)

// 移动平均线4的其他参数
alma4_offst = input.float(group='MA4 other settings', inline='MA41', title='ALMA Offset', defval=0.85, minval=-1, maxval=1, step=0.01)
alma4_sigma = input.float(group='MA4 other settings', inline='MA41', title='Sigma', defval=6, minval=0, maxval=100, step=0.01)
ma4_src = input.source(group='MA4 other settings', inline='MA42', title='Source', defval=close)
ma4_plt_offst = input.int(group='MA4 other settings', inline='MA42', title='Plot offset', defval=0, minval=-500, maxval=500)

// 移动平均线5的其他参数
alma5_offst = input.float(group='MA5 other settings', inline='MA51', title='ALMA Offset', defval=0.85, minval=-1, maxval=1, step=0.01)
alma5_sigma = input.float(group='MA5 other settings', inline='MA51', title='Sigma', defval=6, minval=0, maxval=100, step=0.01)
ma5_src = input.source(group='MA5 other settings', inline='MA52', title='Source', defval=close)
ma5_plt_offst = input.int(group='MA5 other settings', inline='MA52', title='Plot offset', defval=0, minval=-500, maxval=500)

// 移动平均线6的其他参数
alma6_offst = input.float(group='MA6 other settings', inline='MA61', title='ALMA Offset', defval=0.85, minval=-1, maxval=1, step=0.01)
alma6_sigma = input.float(group='MA6 other settings', inline='MA61', title='Sigma', defval=6, minval=0, maxval=100, step=0.01)
ma6_src = input.source(group='MA6 other settings', inline='MA62', title='Source', defval=close)
ma6_plt_offst = input.int(group='MA6 other settings', inline='MA62', title='Plot offset', defval=0, minval=-500, maxval=500)

// 移动平均线7的其他参数
alma7_offst = input.float(group='MA7 other settings', inline='MA71', title='ALMA Offset', defval=0.85, minval=-1, maxval=1, step=0.01)
alma7_sigma = input.float(group='MA7 other settings', inline='MA71', title='Sigma', defval=6, minval=0, maxval=100, step=0.01)
ma7_src = input.source(group='MA7 other settings', inline='MA72', title='Source', defval=close)
ma7_plt_offst = input.int(group='MA7 other settings', inline='MA72', title='Plot offset', defval=0, minval=-500, maxval=500)

// ==================== 背景填充设置 ====================
// 移动平均线之间的背景填充开关：用于突出显示不同MA之间的区域
fill_12_on = input.bool(group='Background fills between MAs', inline='FILL1', title='MA1-2', defval=false)   // MA1和MA2之间填充
fill_23_on = input.bool(group='Background fills between MAs', inline='FILL2', title='MA2-3', defval=true)    // MA2和MA3之间填充
fill_34_on = input.bool(group='Background fills between MAs', inline='FILL3', title='MA3-4', defval=false)   // MA3和MA4之间填充
fill_45_on = input.bool(group='Background fills between MAs', inline='FILL4', title='MA4-5', defval=true)    // MA4和MA5之间填充
fill_56_on = input.bool(group='Background fills between MAs', inline='FILL5', title='MA5-6', defval=false)   // MA5和MA6之间填充
fill_67_on = input.bool(group='Background fills between MAs', inline='FILL6', title='MA6-7', defval=true)    // MA6和MA7之间填充

// 背景填充透明度设置：数值越大越透明（0=完全不透明，100=完全透明）
fill_12_trans = input.int(group='Background fills between MAs', inline='FILL1', title=', transparency', defval=70, minval=0, maxval=100)
fill_23_trans = input.int(group='Background fills between MAs', inline='FILL2', title=', transparency', defval=70, minval=0, maxval=100)
fill_34_trans = input.int(group='Background fills between MAs', inline='FILL3', title=', transparency', defval=70, minval=0, maxval=100)
fill_45_trans = input.int(group='Background fills between MAs', inline='FILL4', title=', transparency', defval=70, minval=0, maxval=100)
fill_56_trans = input.int(group='Background fills between MAs', inline='FILL5', title=', transparency', defval=70, minval=0, maxval=100)
fill_67_trans = input.int(group='Background fills between MAs', inline='FILL6', title=', transparency', defval=70, minval=0, maxval=100)

// ==================== 标签设置 ====================
// 移动平均线标签显示开关：控制是否在图表上显示信息标签
ma1_tag_on  = input.bool(group='Label', inline='LBL1', title='MA1,', defval=false)
ma2_tag_on  = input.bool(group='Label', inline='LBL1', title='MA2,', defval=false)
ma3_tag_on  = input.bool(group='Label', inline='LBL1', title='MA3,', defval=false)
ma4_tag_on  = input.bool(group='Label', inline='LBL1', title='MA4,', defval=false)
ma5_tag_on  = input.bool(group='Label', inline='LBL1', title='MA5,', defval=false)
ma6_tag_on  = input.bool(group='Label', inline='LBL1', title='MA6,', defval=false)
ma7_tag_on  = input.bool(group='Label', inline='LBL1', title='MA7', defval=false)

// 标签内容设置：定义标签中每行显示的信息类型
tag_row_1 = input.string(group='Label', title='Row 1 text', defval='Price Bar', options=['Date', 'Peroid', 'Price Bar', 'Price MA', 'Type'])  // 第1行：价格柱信息
tag_row_2 = input.string(group='Label', title='Row 2 text', defval='Price MA',  options=['none', 'Date', 'Peroid', 'Price Bar', 'Price MA', 'Type'])  // 第2行：MA价格
tag_row_3 = input.string(group='Label', title='Row 3 text', defval='Date',      options=['none', 'Date', 'Peroid', 'Price Bar', 'Price MA', 'Type'])  // 第3行：日期
tag_row_4 = input.string(group='Label', title='Row 4 text', defval='Peroid',    options=['none', 'Date', 'Peroid', 'Price Bar', 'Price MA', 'Type'])  // 第4行：周期
tag_row_5 = input.string(group='Label', title='Row 5 text', defval='Type',      options=['none', 'Date', 'Peroid', 'Price Bar', 'Price MA', 'Type'])  // 第5行：类型

// ==================== 价格线设置 ====================
// 移动平均线价格线显示开关：在图表右侧显示当前MA价格的水平线
ma1_price_line_on  = input.bool(group='Price lines', inline='PRCL1', title='MA1,', defval=false)
ma2_price_line_on  = input.bool(group='Price lines', inline='PRCL1', title='MA2,', defval=false)
ma3_price_line_on  = input.bool(group='Price lines', inline='PRCL1', title='MA3,', defval=false)
ma4_price_line_on  = input.bool(group='Price lines', inline='PRCL1', title='MA4,', defval=false)
ma5_price_line_on  = input.bool(group='Price lines', inline='PRCL1', title='MA5,', defval=false)
ma6_price_line_on  = input.bool(group='Price lines', inline='PRCL1', title='MA6,', defval=false)
ma7_price_line_on  = input.bool(group='Price lines', inline='PRCL1', title='MA7', defval=false)

// ==================== 移动平均线计算 ====================

// 初始移动平均线计算：使用默认函数计算所有移动平均线
// 返回值包含：[移动平均线值, 是否需要周期长度参数]
[ma1, ma1_has_len] = defaultFunction(def_fn, ma1_src, ma1_len, alma1_offst, alma1_sigma)
[ma2, ma2_has_len] = defaultFunction(def_fn, ma2_src, ma2_len, alma2_offst, alma2_sigma)
[ma3, ma3_has_len] = defaultFunction(def_fn, ma3_src, ma3_len, alma3_offst, alma3_sigma)
[ma4, ma4_has_len] = defaultFunction(def_fn, ma4_src, ma4_len, alma4_offst, alma4_sigma)
[ma5, ma5_has_len] = defaultFunction(def_fn, ma5_src, ma5_len, alma5_offst, alma5_sigma)
[ma6, ma6_has_len] = defaultFunction(def_fn, ma6_src, ma6_len, alma6_offst, alma6_sigma)
[ma7, ma7_has_len] = defaultFunction(def_fn, ma7_src, ma7_len, alma7_offst, alma7_sigma)

// 移动平均线1的自定义计算：如果不使用默认函数，则根据选择的类型重新计算
if ma1_fn != 'default'  // 如果MA1不使用默认函数
    if ma1_fn == 'ALMA'                                 // Arnaud Legoux移动平均线
        ma1 := ta.alma(ma1_src, ma1_len, alma1_offst, alma1_sigma)
        ma1_has_len := true
    else if ma1_fn == 'EMA'                             // 指数移动平均线
        ma1 := ta.ema(ma1_src, ma1_len)
        ma1_has_len := true
    else if ma1_fn == 'RMA'                             // 相对强度移动平均线
        ma1 := ta.rma(ma1_src, ma1_len)
        ma1_has_len := true
    else if ma1_fn == 'SMA'                             // 简单移动平均线
        ma1 := ta.sma(ma1_src, ma1_len)
        ma1_has_len := true
    else if ma1_fn == 'SWMA'                            // 对称加权移动平均线
        ma1 := ta.swma(ma1_src)
        ma1_has_len := false
    else if ma1_fn == 'VWAP'                            // 成交量加权平均价格
        ma1 := ta.vwap(ma1_src)
        ma1_has_len := false
    else if ma1_fn == 'VWMA'                            // 成交量加权移动平均线
        ma1 := ta.vwma(ma1_src, ma1_len)
        ma1_has_len := true
    else if ma1_fn == 'WMA'                             // 线性加权移动平均线
        ma1 := ta.wma(ma1_src, ma1_len)
        ma1_has_len := true

// 移动平均线2的自定义计算：如果不使用默认函数，则根据选择的类型重新计算
if ma2_fn != 'default'  // 如果MA2不使用默认函数
    if ma2_fn == 'ALMA'
        ma2 := ta.alma(ma2_src, ma2_len, alma2_offst, alma2_sigma)
        ma2_has_len := true
    else if ma2_fn == 'EMA'
        ma2 := ta.ema(ma2_src, ma2_len)
        ma2_has_len := true
    else if ma2_fn == 'RMA'
        ma2 := ta.rma(ma2_src, ma2_len)
        ma2_has_len := true
    else if ma2_fn == 'SMA'
        ma2 := ta.sma(ma2_src, ma2_len)
        ma2_has_len := true
    else if ma2_fn == 'SWMA'
        ma2 := ta.swma(ma2_src)
        ma2_has_len := false
    else if ma2_fn == 'VWAP'
        ma2 := ta.vwap(ma2_src)
        ma2_has_len := false
    else if ma2_fn == 'VWMA'
        ma2 := ta.vwma(ma2_src, ma2_len)                // 修复：使用正确的ma2_len参数
        ma2_has_len := true
    else if ma2_fn == 'WMA'
        ma2 := ta.wma(ma2_src, ma2_len)
        ma2_has_len := true

// 移动平均线3的自定义计算：如果不使用默认函数，则根据选择的类型重新计算
if ma3_fn != 'default'  // 如果MA3不使用默认函数
    if ma3_fn == 'ALMA'
        ma3 := ta.alma(ma3_src, ma3_len, alma3_offst, alma3_sigma)
        ma3_has_len := true
    else if ma3_fn == 'EMA'
        ma3 := ta.ema(ma3_src, ma3_len)
        ma3_has_len := true
    else if ma3_fn == 'RMA'
        ma3 := ta.rma(ma3_src, ma3_len)
        ma3_has_len := true
    else if ma3_fn == 'SMA'
        ma3 := ta.sma(ma3_src, ma3_len)
        ma3_has_len := true
    else if ma3_fn == 'SWMA'
        ma3 := ta.swma(ma3_src)
        ma3_has_len := false
    else if ma3_fn == 'VWAP'
        ma3 := ta.vwap(ma3_src)
        ma3_has_len := false
    else if ma3_fn == 'VWMA'
        ma3 := ta.vwma(ma3_src, ma3_len)
        ma3_has_len := true
    else if ma3_fn == 'WMA'
        ma3 := ta.wma(ma3_src, ma3_len)
        ma3_has_len := true

// 移动平均线4的自定义计算：如果不使用默认函数，则根据选择的类型重新计算
if ma4_fn != 'default'  // 如果MA4不使用默认函数
    if ma4_fn == 'ALMA'
        ma4 := ta.alma(ma4_src, ma4_len, alma4_offst, alma4_sigma)
        ma4_has_len := true
    else if ma4_fn == 'EMA'
        ma4 := ta.ema(ma4_src, ma4_len)
        ma4_has_len := true
    else if ma4_fn == 'RMA'
        ma4 := ta.rma(ma4_src, ma4_len)
        ma4_has_len := true
    else if ma4_fn == 'SMA'
        ma4 := ta.sma(ma4_src, ma4_len)
        ma4_has_len := true
    else if ma4_fn == 'SWMA'
        ma4 := ta.swma(ma4_src)
        ma4_has_len := false
    else if ma4_fn == 'VWAP'
        ma4 := ta.vwap(ma4_src)
        ma4_has_len := false
    else if ma4_fn == 'VWMA'
        ma4 := ta.vwma(ma4_src, ma4_len)
        ma4_has_len := true
    else if ma4_fn == 'WMA'
        ma4 := ta.wma(ma4_src, ma4_len)
        ma4_has_len := true

// 移动平均线5的自定义计算：如果不使用默认函数，则根据选择的类型重新计算
if ma5_fn != 'default'  // 如果MA5不使用默认函数
    if ma5_fn == 'ALMA'
        ma5 := ta.alma(ma5_src, ma5_len, alma5_offst, alma5_sigma)
        ma5_has_len := true
    else if ma5_fn == 'EMA'
        ma5 := ta.ema(ma5_src, ma5_len)
        ma5_has_len := true
    else if ma5_fn == 'RMA'
        ma5 := ta.rma(ma5_src, ma5_len)
        ma5_has_len := true
    else if ma5_fn == 'SMA'
        ma5 := ta.sma(ma5_src, ma5_len)
        ma5_has_len := true
    else if ma5_fn == 'SWMA'
        ma5 := ta.swma(ma5_src)
        ma5_has_len := false
    else if ma5_fn == 'VWAP'
        ma5 := ta.vwap(ma5_src)
        ma5_has_len := false
    else if ma5_fn == 'VWMA'
        ma5 := ta.vwma(ma5_src, ma5_len)
        ma5_has_len := true
    else if ma5_fn == 'WMA'
        ma5 := ta.wma(ma5_src, ma5_len)
        ma5_has_len := true

// 移动平均线6的自定义计算：如果不使用默认函数，则根据选择的类型重新计算
if ma6_fn != 'default'  // 如果MA6不使用默认函数
    if ma6_fn == 'ALMA'
        ma6 := ta.alma(ma6_src, ma6_len, alma6_offst, alma6_sigma)
        ma6_has_len := true
    else if ma6_fn == 'EMA'
        ma6 := ta.ema(ma6_src, ma6_len)
        ma6_has_len := true
    else if ma6_fn == 'RMA'
        ma6 := ta.rma(ma6_src, ma6_len)
        ma6_has_len := true
    else if ma6_fn == 'SMA'
        ma6 := ta.sma(ma6_src, ma6_len)
        ma6_has_len := true
    else if ma6_fn == 'SWMA'
        ma6 := ta.swma(ma6_src)
        ma6_has_len := false
    else if ma6_fn == 'VWAP'
        ma6 := ta.vwap(ma6_src)
        ma6_has_len := false
    else if ma6_fn == 'VWMA'
        ma6 := ta.vwma(ma6_src, ma6_len)
        ma6_has_len := true
    else if ma6_fn == 'WMA'
        ma6 := ta.wma(ma6_src, ma6_len)
        ma6_has_len := true

// 移动平均线7的自定义计算：如果不使用默认函数，则根据选择的类型重新计算
if ma7_fn != 'default'  // 如果MA7不使用默认函数
    if ma7_fn == 'ALMA'
        ma7 := ta.alma(ma7_src, ma7_len, alma7_offst, alma7_sigma)  // 修复：使用正确的ma7_src参数
        ma7_has_len := true
    else if ma7_fn == 'EMA'
        ma7 := ta.ema(ma7_src, ma7_len)
        ma7_has_len := true
    else if ma7_fn == 'RMA'
        ma7 := ta.rma(ma7_src, ma7_len)
        ma7_has_len := true
    else if ma7_fn == 'SMA'
        ma7 := ta.sma(ma7_src, ma7_len)
        ma7_has_len := true
    else if ma7_fn == 'SWMA'
        ma7 := ta.swma(ma7_src)
        ma7_has_len := false
    else if ma7_fn == 'VWAP'
        ma7 := ta.vwap(ma7_src)
        ma7_has_len := false
    else if ma7_fn == 'VWMA'
        ma7 := ta.vwma(ma7_src, ma7_len)
        ma7_has_len := true
    else if ma7_fn == 'WMA'
        ma7 := ta.wma(ma7_src, ma7_len)
        ma7_has_len := true

// ==================== 绘制移动平均线 ====================
// 绘制移动平均线曲线：只有当开关打开时才显示，否则显示na（不显示）
p1 = plot(series=ma1_on ? ma1 : na, color=ma1_clr, trackprice=false, offset=ma1_plt_offst)  // 绘制MA1
p2 = plot(series=ma2_on ? ma2 : na, color=ma2_clr, trackprice=false, offset=ma2_plt_offst)  // 绘制MA2
p3 = plot(series=ma3_on ? ma3 : na, color=ma3_clr, trackprice=false, offset=ma3_plt_offst)  // 绘制MA3
p4 = plot(series=ma4_on ? ma4 : na, color=ma4_clr, trackprice=false, offset=ma4_plt_offst)  // 绘制MA4
p5 = plot(series=ma5_on ? ma5 : na, color=ma5_clr, trackprice=false, offset=ma5_plt_offst)  // 绘制MA5
p6 = plot(series=ma6_on ? ma6 : na, color=ma6_clr, trackprice=false, offset=ma6_plt_offst)  // 绘制MA6
p7 = plot(series=ma7_on ? ma7 : na, color=ma7_clr, trackprice=false, offset=ma7_plt_offst)  // 绘制MA7

// ==================== 移动平均线之间的背景填充 ====================
// 在移动平均线之间填充颜色，颜色根据MA的相对位置动态选择
fill(p1, p2, color.new((ma1 > ma2 ? ma1_clr : ma2_clr), (ma1_on and ma2_on and fill_12_on ? fill_12_trans : 100)))  // MA1和MA2之间填充
fill(p2, p3, color.new((ma2 > ma3 ? ma2_clr : ma3_clr), (ma2_on and ma3_on and fill_23_on ? fill_23_trans : 100)))  // MA2和MA3之间填充
fill(p3, p4, color.new((ma3 > ma4 ? ma3_clr : ma4_clr), (ma3_on and ma4_on and fill_34_on ? fill_34_trans : 100)))  // MA3和MA4之间填充
fill(p4, p5, color.new((ma4 > ma5 ? ma4_clr : ma5_clr), (ma4_on and ma5_on and fill_45_on ? fill_45_trans : 100)))  // MA4和MA5之间填充
fill(p5, p6, color.new((ma5 > ma6 ? ma5_clr : ma6_clr), (ma5_on and ma6_on and fill_56_on ? fill_56_trans : 100)))  // MA5和MA6之间填充
fill(p6, p7, color.new((ma6 > ma7 ? ma6_clr : ma7_clr), (ma6_on and ma7_on and fill_67_on ? fill_67_trans : 100)))  // MA6和MA7之间填充

// ==================== 移动平均线价格线 ====================
// 在图表右侧显示移动平均线的当前价格水平线（虚线样式）
if ma1_on and ma1_price_line_on                        // 如果MA1开启且价格线开启
    ma1_price = line.new(x1=bar_index + 4, y1=ma1, x2=bar_index + 5, y2=ma1, xloc=xloc.bar_index, extend=extend.right, color=ma1_clr, style=line.style_dotted, width=1)
    line.delete(ma1_price[1])                           // 删除前一个价格线，避免重复

if ma2_on and ma2_price_line_on
    ma2_price = line.new(x1=bar_index + 4, y1=ma2, x2=bar_index + 5, y2=ma2, xloc=xloc.bar_index, extend=extend.right, color=ma2_clr, style=line.style_dotted, width=1)
    line.delete(ma2_price[1])

if ma3_on and ma3_price_line_on
    ma3_price = line.new(x1=bar_index + 4, y1=ma3, x2=bar_index + 5, y2=ma3, xloc=xloc.bar_index, extend=extend.right, color=ma3_clr, style=line.style_dotted, width=1)
    line.delete(ma3_price[1])

if ma4_on and ma4_price_line_on
    ma4_price = line.new(x1=bar_index + 4, y1=ma4, x2=bar_index + 5, y2=ma4, xloc=xloc.bar_index, extend=extend.right, color=ma4_clr, style=line.style_dotted, width=1)
    line.delete(ma4_price[1])

if ma5_on and ma5_price_line_on
    ma5_price = line.new(x1=bar_index + 4, y1=ma5, x2=bar_index + 5, y2=ma5, xloc=xloc.bar_index, extend=extend.right, color=ma5_clr, style=line.style_dotted, width=1)
    line.delete(ma5_price[1])

if ma6_on and ma6_price_line_on
    ma6_price = line.new(x1=bar_index + 4, y1=ma6, x2=bar_index + 5, y2=ma6, xloc=xloc.bar_index, extend=extend.right, color=ma6_clr, style=line.style_dotted, width=1)
    line.delete(ma6_price[1])

if ma7_on and ma7_price_line_on
    ma7_price = line.new(x1=bar_index + 4, y1=ma7, x2=bar_index + 5, y2=ma7, xloc=xloc.bar_index, extend=extend.right, color=ma7_clr, style=line.style_dotted, width=1)
    line.delete(ma7_price[1])

// ==================== 标签文本生成函数 ====================
// 生成标签行文本的函数：根据指定的信息类型生成相应的文本内容
// 参数：
//   name - 信息类型（Date/Period/Type/Price Bar/Price MA）
//   head - 是否为第一行（影响是否添加换行符）
//   fn - 移动平均线函数类型
//   len - 周期长度
//   indx - 数组索引
//   src - 数据源
//   ma - 移动平均线值
rowText(name, head, fn, len, indx, src, ma) =>
    row_text = ''
    if name == 'Date'                                   // 日期信息：显示月-日格式
        if head
            row_text += str.tostring(month(time[indx]))+'-'+str.tostring(dayofmonth(time[indx]))
        else
            row_text += '\n' + str.tostring(month(time[indx]))+'-'+str.tostring(dayofmonth(time[indx]))
    else if name == 'Peroid'                            // 周期信息：显示周期长度
        if head
            row_text += str.tostring(len)
        else
            row_text += '\n' + str.tostring(len)
    else if name == 'Type'                              // 类型信息：显示MA类型
        if head
            row_text += (fn == 'default' ? def_fn : fn)
        else
            row_text += '\n' + (fn == 'default' ? def_fn : fn)
    else if name == 'Price Bar'                         // 价格柱信息：显示指定位置的价格
        if head
            row_text += strRoundValue(src[indx])
        else
            row_text += '\n' + strRoundValue(src[indx])
    else if name == 'Price MA'                          // MA价格信息：显示指定位置的MA值
        if head
            row_text += strRoundValue(ma[indx])
        else
            row_text += '\n' + strRoundValue(ma[indx])
    (row_text)

// ==================== 组合标签文本 ====================
// 为每个移动平均线组合标签文本：将5行信息组合成完整的标签内容

// 组合MA1标签文本
_row1 = rowText(tag_row_1, true,  ma1_fn, ma1_len, ma1_len_indx, ma1_src, ma1)
_row2 = rowText(tag_row_2, false, ma1_fn, ma1_len, ma1_len_indx, ma1_src, ma1)
_row3 = rowText(tag_row_3, false, ma1_fn, ma1_len, ma1_len_indx, ma1_src, ma1)
_row4 = rowText(tag_row_4, false, ma1_fn, ma1_len, ma1_len_indx, ma1_src, ma1)
_row5 = rowText(tag_row_5, false, ma1_fn, ma1_len, ma1_len_indx, ma1_src, ma1)
ma1_tag_txt = _row1 + _row2 + _row3 + _row4 + _row5

// 组合MA2标签文本
_row1 := rowText(tag_row_1, true,  ma2_fn, ma2_len, ma2_len_indx, ma2_src, ma2)
_row2 := rowText(tag_row_2, false, ma2_fn, ma2_len, ma2_len_indx, ma2_src, ma2)
_row3 := rowText(tag_row_3, false, ma2_fn, ma2_len, ma2_len_indx, ma2_src, ma2)
_row4 := rowText(tag_row_4, false, ma2_fn, ma2_len, ma2_len_indx, ma2_src, ma2)
_row5 := rowText(tag_row_5, false, ma2_fn, ma2_len, ma2_len_indx, ma2_src, ma2)
ma2_tag_txt = _row1 + _row2 + _row3 + _row4 + _row5

// 组合MA3标签文本
_row1 := rowText(tag_row_1, true,  ma3_fn, ma3_len, ma3_len_indx, ma3_src, ma3)
_row2 := rowText(tag_row_2, false, ma3_fn, ma3_len, ma3_len_indx, ma3_src, ma3)
_row3 := rowText(tag_row_3, false, ma3_fn, ma3_len, ma3_len_indx, ma3_src, ma3)
_row4 := rowText(tag_row_4, false, ma3_fn, ma3_len, ma3_len_indx, ma3_src, ma3)
_row5 := rowText(tag_row_5, false, ma3_fn, ma3_len, ma3_len_indx, ma3_src, ma3)
ma3_tag_txt = _row1 + _row2 + _row3 + _row4 + _row5

// 组合MA4标签文本
_row1 := rowText(tag_row_1, true,  ma4_fn, ma4_len, ma4_len_indx, ma4_src, ma4)
_row2 := rowText(tag_row_2, false, ma4_fn, ma4_len, ma4_len_indx, ma4_src, ma4)
_row3 := rowText(tag_row_3, false, ma4_fn, ma4_len, ma4_len_indx, ma4_src, ma4)
_row4 := rowText(tag_row_4, false, ma4_fn, ma4_len, ma4_len_indx, ma4_src, ma4)
_row5 := rowText(tag_row_5, false, ma4_fn, ma4_len, ma4_len_indx, ma4_src, ma4)
ma4_tag_txt = _row1 + _row2 + _row3 + _row4 + _row5

// 组合MA5标签文本
_row1 := rowText(tag_row_1, true,  ma5_fn, ma5_len, ma5_len_indx, ma5_src, ma5)
_row2 := rowText(tag_row_2, false, ma5_fn, ma5_len, ma5_len_indx, ma5_src, ma5)
_row3 := rowText(tag_row_3, false, ma5_fn, ma5_len, ma5_len_indx, ma5_src, ma5)
_row4 := rowText(tag_row_4, false, ma5_fn, ma5_len, ma5_len_indx, ma5_src, ma5)
_row5 := rowText(tag_row_5, false, ma5_fn, ma5_len, ma5_len_indx, ma5_src, ma5)
ma5_tag_txt = _row1 + _row2 + _row3 + _row4 + _row5

// 组合MA6标签文本
_row1 := rowText(tag_row_1, true,  ma6_fn, ma6_len, ma6_len_indx, ma6_src, ma6)
_row2 := rowText(tag_row_2, false, ma6_fn, ma6_len, ma6_len_indx, ma6_src, ma6)
_row3 := rowText(tag_row_3, false, ma6_fn, ma6_len, ma6_len_indx, ma6_src, ma6)
_row4 := rowText(tag_row_4, false, ma6_fn, ma6_len, ma6_len_indx, ma6_src, ma6)
_row5 := rowText(tag_row_5, false, ma6_fn, ma6_len, ma6_len_indx, ma6_src, ma6)
ma6_tag_txt = _row1 + _row2 + _row3 + _row4 + _row5

// 组合MA7标签文本
_row1 := rowText(tag_row_1, true,  ma7_fn, ma7_len, ma7_len_indx, ma7_src, ma7)
_row2 := rowText(tag_row_2, false, ma7_fn, ma7_len, ma7_len_indx, ma7_src, ma7)
_row3 := rowText(tag_row_3, false, ma7_fn, ma7_len, ma7_len_indx, ma7_src, ma7)
_row4 := rowText(tag_row_4, false, ma7_fn, ma7_len, ma7_len_indx, ma7_src, ma7)
_row5 := rowText(tag_row_5, false, ma7_fn, ma7_len, ma7_len_indx, ma7_src, ma7)
ma7_tag_txt = _row1 + _row2 + _row3 + _row4 + _row5

// ==================== 初始化全局标签变量 ====================
// 创建全局标签变量并立即删除，为后续使用做准备
var label ma1_tag = label.new(bar_index, na)
var label ma2_tag = label.new(bar_index, na)
var label ma3_tag = label.new(bar_index, na)
var label ma4_tag = label.new(bar_index, na)
var label ma5_tag = label.new(bar_index, na)
var label ma6_tag = label.new(bar_index, na)
var label ma7_tag = label.new(bar_index, na)
label.delete(ma1_tag)                                   // 删除初始标签，避免显示空标签
label.delete(ma2_tag)
label.delete(ma3_tag)
label.delete(ma4_tag)
label.delete(ma5_tag)
label.delete(ma6_tag)
label.delete(ma7_tag)

// ==================== 在图表上显示标签 ====================
// 在最后一根K线时，在移动平均线起始位置显示信息标签
if barstate.islast                                      // 只在最后一根K线时执行
    // MA1标签：在MA1起始位置显示标签，标签样式根据该位置K线的涨跌情况决定
    if ma1_on and ma1_tag_on and ma1_has_len
        ma1_tag := label.new(bar_index - ma1_len_indx, na, ma1_tag_txt, color=ma1_clr, textcolor=color.white, style=close[ma1_len_indx] > open[ma1_len_indx] ? label.style_label_down : label.style_label_up, yloc=close[ma1_len_indx] > open[ma1_len_indx] ? yloc.abovebar : yloc.belowbar)

    // MA2标签
    if ma2_on and ma2_tag_on and ma2_has_len
        ma2_tag := label.new(bar_index - ma2_len_indx, na, ma2_tag_txt, color=ma2_clr, textcolor=color.white, style=close[ma2_len_indx] > open[ma2_len_indx] ? label.style_label_down : label.style_label_up, yloc=close[ma2_len_indx] > open[ma2_len_indx] ? yloc.abovebar : yloc.belowbar)

    // MA3标签
    if ma3_on and ma3_tag_on and ma3_has_len
        ma3_tag := label.new(bar_index - ma3_len_indx, na, ma3_tag_txt, color=ma3_clr, textcolor=color.white, style=close[ma3_len_indx] > open[ma3_len_indx] ? label.style_label_down : label.style_label_up, yloc=close[ma3_len_indx] > open[ma3_len_indx] ? yloc.abovebar : yloc.belowbar)

    // MA4标签
    if ma4_on and ma4_tag_on and ma4_has_len
        ma4_tag := label.new(bar_index - ma4_len_indx, na, ma4_tag_txt, color=ma4_clr, textcolor=color.white, style=close[ma4_len_indx] > open[ma4_len_indx] ? label.style_label_down : label.style_label_up, yloc=close[ma4_len_indx] > open[ma4_len_indx] ? yloc.abovebar : yloc.belowbar)

    // MA5标签
    if ma5_on and ma5_tag_on and ma5_has_len
        ma5_tag := label.new(bar_index - ma5_len_indx, na, ma5_tag_txt, color=ma5_clr, textcolor=color.white, style=close[ma5_len_indx] > open[ma5_len_indx] ? label.style_label_down : label.style_label_up, yloc=close[ma5_len_indx] > open[ma5_len_indx] ? yloc.abovebar : yloc.belowbar)

    // MA6标签
    if ma6_on and ma6_tag_on and ma6_has_len
        ma6_tag := label.new(bar_index - ma6_len_indx, na, ma6_tag_txt, color=ma6_clr, textcolor=color.white, style=close[ma6_len_indx] > open[ma6_len_indx] ? label.style_label_down : label.style_label_up, yloc=close[ma6_len_indx] > open[ma6_len_indx] ? yloc.abovebar : yloc.belowbar)

    // MA7标签
    if ma7_on and ma7_tag_on and ma7_has_len
        ma7_tag := label.new(bar_index - ma7_len_indx, na, ma7_tag_txt, color=ma7_clr, textcolor=color.white, style=close[ma7_len_indx] > open[ma7_len_indx] ? label.style_label_down : label.style_label_up, yloc=close[ma7_len_indx] > open[ma7_len_indx] ? yloc.abovebar : yloc.belowbar)

// ==================== 代码结束 ====================
// 此Pine Script指标提供了一个功能完整的多移动平均线系统，包括：
// 1. 支持8种不同类型的移动平均线（ALMA, EMA, RMA, SMA, SWMA, VWAP, VWMA, WMA）
// 2. 可同时显示最多7条移动平均线，每条都可独立配置
// 3. 支持移动平均线之间的背景填充，便于观察趋势
// 4. 提供价格线功能，在图表右侧显示当前MA价格
// 5. 支持信息标签，显示详细的MA参数和价格信息
// 6. 所有功能都可通过用户界面灵活配置

