2025-01-20 12:07:05.9  正在从[1]号服务器获取时间...


2025-01-20 12:07:07.2  正在从[2]号服务器获取时间...


2025-01-20 12:07:07.5  [网络时间]：2025-01-20 12:07:07.381


2025-01-20 12:07:07.3  [同步成功]已完成自动同步本机时间。


2025-01-20 12:07:36.3  初始化完毕，公网IP：*************


2025-01-20 12:07:36.8  链接12306服务器速度:231毫秒[优]


2025-01-20 12:07:36.9  您的VIP已过期,已恢复为免费用户,续期请重新注册


2025-01-20 12:07:38.3  获取到:485个CDN,开始智能测速中...


2025-01-20 12:07:55.0  已屏蔽[鲘门]，会记忆保存，要显示[鲘门]请恢复勾选。


2025-01-20 12:07:55.5  已屏蔽[陆丰]，会记忆保存，要显示[陆丰]请恢复勾选。


2025-01-20 12:07:56.0  已屏蔽[陆丰东]，会记忆保存，要显示[陆丰东]请恢复勾选。


2025-01-20 12:07:58.6  已屏蔽[陆丰南]，会记忆保存，要显示[陆丰南]请恢复勾选。


2025-01-20 12:08:02.3  已屏蔽[汕尾]，会记忆保存，要显示[汕尾]请恢复勾选。


2025-01-20 12:08:25.2  查询完毕，查到153个车次，筛选显示102个，用时:304毫秒。


2025-01-20 12:08:34.4  查询完毕，查到153个车次，筛选显示85个，用时:77毫秒。


2025-01-20 12:09:13.9  查询完毕，查到153个车次，筛选显示82个，用时:51毫秒。


2025-01-20 12:09:38.5  链接12306服务器速度:103毫秒[优]


2025-01-20 12:09:57.3  已保存增开同城的选择-->设置的车站才会买


2025-01-20 12:10:16.4  [自动处理]默认设置[改签不抢增开列车]，如需更改，请修改增开设置。


2025-01-20 12:10:16.6  查询完毕，查到139个车次，筛选显示78个，用时:220毫秒。


2025-01-20 12:10:32.4  查询完毕，查到153个车次，筛选显示82个，用时:219毫秒。


2025-01-20 12:10:47.6  查询完毕，查到153个车次，筛选显示82个，用时:53毫秒。


2025-01-20 12:11:02.3  已屏蔽[福田]，会记忆保存，要显示[福田]请恢复勾选。


2025-01-20 12:11:02.8  已屏蔽[深圳]，会记忆保存，要显示[深圳]请恢复勾选。


2025-01-20 12:11:04.2  查询完毕，查到153个车次，筛选显示74个，用时:147毫秒。


2025-01-20 12:11:11.0  查询完毕，查到153个车次，筛选显示74个，用时:50毫秒。


2025-01-20 12:11:38.4  查询完毕，查到153个车次，筛选显示77个，用时:181毫秒。


2025-01-20 12:11:40.5  查询完毕，查到153个车次，筛选显示77个，用时:32毫秒。


2025-01-20 12:11:49.1  查询完毕，查到153个车次，筛选显示77个，用时:211毫秒。


2025-01-20 12:12:42.7  添加任务成功，1、[改签]深圳北>汕尾_2025-01-24_黎家能[成人]_二等座;无座;一等座_D2282;G6304;G3006;D2310;D2388;G6178;D2334;D378;D7200;G6328;D2306;D2102;D2424;G6356;D684;D3108;D3308;G6364;D3112;G6086;D2362;D9702;D3124;D3336;D2324;G3142;D2384;D2344;D2354;G3744;D2412;G9644;D2432;D2356;D2298;G6392;D2304;D2336;D2330;G1610;G3002;D3344;G2934;G6316;D3338;D2318;G3004;D3334;D672;D2326;D3340;D2312;D7396;G6578;D9708;G6332;D3316;D7402;D690;G6082;D2398;D7324;D676;G1178;G3008;D7424;G6092;D7422;D7344;D9746;D9796;D7342;D7404;D7346;G6532;G2968;D9714


2025-01-20 12:12:48.6  查询完毕，查到148个车次，筛选显示76个，用时:235毫秒。


2025-01-20 12:12:58.3  添加任务成功，2、深圳北>汕尾_2025-01-25_黎家能[成人]_二等座;无座;一等座_D2282;G6304;G3006;D2310;D2388;G6178;D2334;D378;G6328;D2306;D2102;D2424;G6356;D684;D3108;D3308;G6364;D3112;G6086;D2362;D9702;D3124;D3336;D2324;G3142;D2384;D2344;D2354;G3744;D2412;G9644;D2432;D2356;D2298;G6392;D2304;D2336;D2330;G1610;G3002;D3344;G2934;G6316;D3338;D2318;G3004;D3334;D672;D2326;D3340;D2312;D7396;G6578;D9708;G6332;D3316;D7402;D690;G6082;D2398;D7324;D676;G1178;G3008;D7424;G6092;D7422;D7344;D9746;D9796;D7342;D7404;D7346;G6532;G2968;D9714


2025-01-20 12:13:29.7  [多任务]开始查询2025-01-24,2025-01-25车次，间隔：默认


2025-01-20 12:13:36.6  [多任务1]深圳北>汕尾(01-24)的[D3108,二等座]...


2025-01-20 12:13:36.7  [预定]线程启动完成,开始解析算法...


2025-01-20 12:13:36.7  [任务1]查询订单并改签中...


2025-01-20 12:13:37.3  [任务1]申请改签成功,申请订单中...


2025-01-20 12:13:37.3  [任务1]算法解析完成,开始申请订单...


2025-01-20 12:13:37.4  [任务2]申请订单成功,开始校验订单...


2025-01-20 12:13:37.8  [任务3]订单效验完成,正在获取余票...


2025-01-20 12:13:37.9  [任务4]目前排队人数已经超过余票张数


2025-01-20 12:13:37.9  [小黑屋]该数据为缓存,将D3108车次(O)放入小黑屋120秒...


2025-01-20 12:13:37.9  [多任务]开始查询2025-01-24,2025-01-25车次，间隔：默认


2025-01-20 12:22:38.5  链接12306服务器速度:66毫秒[优]


2025-01-20 12:24:38.8  链接12306服务器速度:61毫秒[优]


2025-01-20 12:33:59.1  [多任务1]深圳北>汕尾(01-24)的[D3108,二等座]...


2025-01-20 12:33:59.1  [预定]线程启动完成,开始解析算法...


2025-01-20 12:33:59.1  [任务1]查询订单并改签中...


2025-01-20 12:33:59.6  [任务1]申请改签成功,申请订单中...


2025-01-20 12:33:59.7  [任务2]申请订单成功,开始校验订单...


2025-01-20 12:34:00.1  [任务3]订单效验完成,正在获取余票...


2025-01-20 12:34:00.2  [任务4]实时无座为(1)张,继续提交...


2025-01-20 12:34:00.5  [提交成功]等待处理,本次用时:1.26秒


2025-01-20 12:34:02.1  [12306]服务器处理中，正在排队...


2025-01-20 12:34:05.2  [12306]服务器处理中，正在排队...


2025-01-20 12:34:08.3  [12306]服务器处理中，正在排队...


2025-01-20 12:34:11.6  [订票成功]车票订单号:E572672097


2025-01-20 12:34:11.8  [锁定订单]坐席为系统分配,已完成锁定


2025-01-20 12:34:11.8  [多任务1]: 恭喜,改签成功!信息如下:乘客:黎家能,日期:2025-01-24,车次:D3108,地点:深圳北11:32-汕尾12:31,席别:二等座,请在18分钟内完成付款,更多信息请查询未付款订单...


2025-01-20 12:34:11.9  [多任务]请先支付当前订单，支付后自动继续剩余任务，如需此功能，请勿重新开始抢票！


2025-01-20 12:34:12.5  [微信通知]发送成功！


2025-01-20 12:38:03.3  检测到用户已支付该订单，[多任务1]已完成，检查到还有勾选的多任务，将继续进行抢票。


2025-01-20 12:38:03.3  [多任务]开始查询2025-01-25车次，间隔：默认


2025-01-20 12:38:03.9  [微信通知]发送成功！


2025-01-20 12:41:15.4  结束车票查询


2025-01-20 12:41:38.1  已屏蔽[西安]，会记忆保存，要显示[西安]请恢复勾选。


2025-01-20 12:41:38.6  已屏蔽[引镇]，会记忆保存，要显示[引镇]请恢复勾选。


2025-01-20 12:41:39.1  已屏蔽[临潼]，会记忆保存，要显示[临潼]请恢复勾选。


2025-01-20 12:41:42.2  查询完毕，查到89个车次，筛选显示60个，用时:137毫秒。


2025-01-20 12:41:50.5  查询完毕，查到78个车次，筛选显示56个，用时:106毫秒。


2025-01-20 12:41:53.8  查询完毕，查到78个车次，筛选显示56个，用时:123毫秒。


2025-01-20 12:42:04.3  查询完毕，查到78个车次，筛选显示67个，用时:130毫秒。


2025-01-20 12:42:08.7  查询完毕，查到78个车次，筛选显示29个，用时:122毫秒。


2025-01-20 12:42:32.9  查询完毕，查到79个车次，筛选显示29个，用时:99毫秒。


2025-01-20 12:42:42.4  已自动修改定时时间为：08:29:58，双击车次快速设置。


2025-01-20 13:33:31.3  用户手动关闭软件，软件已退出。


